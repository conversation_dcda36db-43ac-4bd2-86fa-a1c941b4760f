/**
 * @file vision_data.h
 * @brief 视觉模块数据处理头文件
 * <AUTHOR> Code
 */

#ifndef __VISION_DATA_H
#define __VISION_DATA_H

#include "main.h"
#include "usart.h"
#include <string.h>
#include <stdlib.h>
#include <stdio.h>

/* 数据包类型定义 */
#define VISION_PACKET_TARGET    'T'     // 目标点数据包
#define VISION_PACKET_RECT      'R'     // 矩形数据包  
#define VISION_PACKET_CIRCLE    'C'     // 圆形数据包

/* 数据包格式定义 */
#define VISION_PACKET_START     "$$"    // 数据包开始标志
#define VISION_PACKET_END       "##"    // 数据包结束标志
#define VISION_MAX_CIRCLE_POINTS 12     // 圆形最大坐标点数
#define VISION_BUFFER_SIZE      256     // 接收缓冲区大小

/* 坐标点结构体 */
typedef struct {
    int16_t x;                          // X坐标
    int16_t y;                          // Y坐标
} Vision_Point_t;

/* 目标点数据结构 */
typedef struct {
    int16_t x;                          // 目标X坐标
    int16_t y;                          // 目标Y坐标
    uint8_t valid;                      // 数据有效标志
} Vision_Target_t;

/* 矩形数据结构 */
typedef struct {
    int16_t cx;                         // 矩形中心X坐标
    int16_t cy;                         // 矩形中心Y坐标
    uint8_t valid;                      // 数据有效标志
} Vision_Rect_t;

/* 圆形数据结构 */
typedef struct {
    uint8_t count;                      // 坐标点数量
    Vision_Point_t points[VISION_MAX_CIRCLE_POINTS]; // 坐标点数组
    uint8_t valid;                      // 数据有效标志
} Vision_Circle_t;

/* 视觉数据总结构 */
typedef struct {
    Vision_Target_t target;             // 目标点数据
    Vision_Rect_t rect;                 // 矩形数据
    Vision_Circle_t circle;             // 圆形数据
    uint32_t last_update_time;          // 最后更新时间
} Vision_Data_t;

/* 数据包解析状态 */
typedef enum {
    VISION_STATE_IDLE = 0,              // 空闲状态
    VISION_STATE_START,                 // 检测到开始标志
    VISION_STATE_TYPE,                  // 解析数据类型
    VISION_STATE_DATA,                  // 解析数据内容
    VISION_STATE_END                    // 检测结束标志
} Vision_Parse_State_t;

/* 全局变量声明 */
extern Vision_Data_t g_vision_data;    // 全局视觉数据
extern uint8_t g_vision_rx_buffer[VISION_BUFFER_SIZE]; // 串口接收缓冲区
extern uint16_t g_vision_rx_index;     // 接收缓冲区索引

/* 函数声明 */
void Vision_Init(void);                                 // 视觉模块初始化
void Vision_UART_RxCallback(uint8_t data);             // 串口接收回调函数
uint8_t Vision_Parse_Packet(char* packet);             // 解析数据包
uint8_t Vision_Parse_Target(char* data);               // 解析目标点数据
uint8_t Vision_Parse_Rect(char* data);                 // 解析矩形数据
uint8_t Vision_Parse_Circle(char* data);               // 解析圆形数据
void Vision_Print_Data(void);                          // 打印视觉数据
void Vision_Clear_Data(void);                          // 清除视觉数据
uint8_t Vision_Is_Data_Valid(void);                    // 检查数据是否有效
Vision_Target_t* Vision_Get_Target_Data(void);         // 获取目标点数据
Vision_Rect_t* Vision_Get_Rect_Data(void);             // 获取矩形数据
Vision_Circle_t* Vision_Get_Circle_Data(void);         // 获取圆形数据

#endif /* __VISION_DATA_H */
