/**
 * @file vision_processor.h
 * @brief 视觉模块数据处理头文件
 * <AUTHOR>
 */

#ifndef __VISION_PROCESSOR_H
#define __VISION_PROCESSOR_H

#include "main.h"
#include "usart.h"
#include "MyDefine.h"
#include <string.h>
#include <stdlib.h>
#include <stdio.h>

/* 配置参数 */
#define VISION_MAX_CIRCLE_POINTS    12      // 圆形最大坐标点数（12组）
#define VISION_BUFFER_SIZE          256     // 接收缓冲区大小
#define VISION_TIMEOUT_MS           3000    // 数据超时时间（毫秒）

/* 数据包标识符 */
#define VISION_TARGET_TYPE          'T'     // 目标点数据包标识
#define VISION_RECT_TYPE            'R'     // 矩形数据包标识  
#define VISION_CIRCLE_TYPE          'C'     // 圆形数据包标识

/* 坐标点结构体 */
typedef struct {
    int16_t x;                              // X坐标
    int16_t y;                              // Y坐标
} Vision_Point_t;

/* 目标点数据结构 $$T,150,95## */
typedef struct {
    int16_t x;                              // 目标X坐标
    int16_t y;                              // 目标Y坐标
    uint8_t is_valid;                       // 数据有效标志
    uint32_t timestamp;                     // 数据时间戳
} Vision_Target_Data_t;

/* 矩形数据结构 $$R,cx,cy## */
typedef struct {
    int16_t center_x;                       // 矩形中心X坐标
    int16_t center_y;                       // 矩形中心Y坐标
    uint8_t is_valid;                       // 数据有效标志
    uint32_t timestamp;                     // 数据时间戳
} Vision_Rect_Data_t;

/* 圆形数据结构 $$C,count,x1,y1,x2,y2,...## */
typedef struct {
    uint8_t point_count;                    // 坐标点数量
    Vision_Point_t points[VISION_MAX_CIRCLE_POINTS]; // 坐标点数组（12组）
    uint8_t is_valid;                       // 数据有效标志
    uint32_t timestamp;                     // 数据时间戳
} Vision_Circle_Data_t;

/* 视觉数据总结构 */
typedef struct {
    Vision_Target_Data_t target;            // 目标点数据
    Vision_Rect_Data_t rect;                // 矩形数据
    Vision_Circle_Data_t circle;            // 圆形数据
    uint32_t total_packets_received;        // 总接收包数
    uint32_t parse_error_count;             // 解析错误计数
} Vision_All_Data_t;

/* 数据解析状态枚举 */
typedef enum {
    PARSE_IDLE = 0,                         // 空闲状态
    PARSE_FOUND_START,                      // 找到开始标志$$
    PARSE_GET_TYPE,                         // 获取数据类型
    PARSE_GET_DATA,                         // 获取数据内容
    PARSE_FOUND_END                         // 找到结束标志##
} Vision_Parse_State_t;

/* 全局变量声明 */
extern Vision_All_Data_t g_vision_all_data;        // 全局视觉数据存储
extern uint8_t g_vision_uart_rx_byte;              // 串口接收字节
extern char g_vision_packet_buffer[VISION_BUFFER_SIZE]; // 数据包缓冲区

/* 核心功能函数 */
void Vision_System_Init(void);                     // 视觉系统初始化
void Vision_UART6_RxCallback(uint8_t received_byte); // 串口6接收回调
uint8_t Vision_Parse_Data_Packet(char* packet_str); // 解析数据包
void Vision_Clear_All_Data(void);                  // 清除所有数据
void Vision_Print_All_Data(void);                  // 打印所有数据

/* 数据获取函数 */
Vision_Target_Data_t* Vision_Get_Target_Data(void);    // 获取目标点数据
Vision_Rect_Data_t* Vision_Get_Rect_Data(void);        // 获取矩形数据
Vision_Circle_Data_t* Vision_Get_Circle_Data(void);    // 获取圆形数据

/* 数据检查函数 */
uint8_t Vision_Is_Target_Valid(void);               // 检查目标点数据是否有效
uint8_t Vision_Is_Rect_Valid(void);                 // 检查矩形数据是否有效
uint8_t Vision_Is_Circle_Valid(void);               // 检查圆形数据是否有效
uint8_t Vision_Is_Any_Data_Valid(void);             // 检查是否有任何有效数据

/* 数据处理函数 */
uint8_t Vision_Parse_Target_Packet(char* data_str); // 解析目标点数据包
uint8_t Vision_Parse_Rect_Packet(char* data_str);   // 解析矩形数据包
uint8_t Vision_Parse_Circle_Packet(char* data_str); // 解析圆形数据包

/* 实用工具函数 */
void Vision_Mark_Target_Processed(void);            // 标记目标点已处理
void Vision_Mark_Rect_Processed(void);              // 标记矩形已处理
void Vision_Mark_Circle_Processed(void);            // 标记圆形已处理
uint32_t Vision_Get_Data_Age_Ms(uint32_t timestamp); // 获取数据年龄（毫秒）

#endif /* __VISION_PROCESSOR_H */
