/**
 * @file vision_processor.c
 * @brief 视觉模块数据处理实现文件
 * <AUTHOR>
 */

#include "vision_processor.h"

/* 全局变量定义 */
Vision_All_Data_t g_vision_all_data = {0};         // 全局视觉数据存储
uint8_t g_vision_uart_rx_byte = 0;                 // 串口接收字节
char g_vision_packet_buffer[VISION_BUFFER_SIZE] = {0}; // 数据包缓冲区

/* 内部状态变量 */
static Vision_Parse_State_t parse_state = PARSE_IDLE;  // 解析状态
static uint16_t packet_index = 0;                      // 数据包索引
static uint8_t start_flag_count = 0;                   // 开始标志计数
static uint8_t end_flag_count = 0;                     // 结束标志计数

/**
 * @brief 视觉系统初始化
 */
void Vision_System_Init(void)
{
    // 清除所有数据
    Vision_Clear_All_Data();
    
    // 重置解析状态
    parse_state = PARSE_IDLE;
    packet_index = 0;
    start_flag_count = 0;
    end_flag_count = 0;
    
    // 启动串口6中断接收
    HAL_UART_Receive_IT(&huart6, &g_vision_uart_rx_byte, 1);
    
    my_printf(&huart1, "视觉数据处理系统初始化完成\r\n");
    my_printf(&huart1, "等待串口6数据: 目标点$$T,x,y## 矩形$$R,cx,cy## 圆形$$C,count,x1,y1,...##\r\n");
}

/**
 * @brief 串口6接收中断回调函数
 * @param received_byte 接收到的字节
 */
void Vision_UART6_RxCallback(uint8_t received_byte)
{
    switch(parse_state)
    {
        case PARSE_IDLE:
            // 检测开始标志 $$
            if(received_byte == '$') {
                start_flag_count++;
                if(start_flag_count >= 2) {
                    parse_state = PARSE_GET_TYPE;
                    packet_index = 0;
                    start_flag_count = 0;
                    memset(g_vision_packet_buffer, 0, VISION_BUFFER_SIZE);
                }
            } else {
                start_flag_count = 0;
            }
            break;
            
        case PARSE_GET_TYPE:
            // 获取数据类型 T/R/C
            if(received_byte == VISION_TARGET_TYPE || 
               received_byte == VISION_RECT_TYPE || 
               received_byte == VISION_CIRCLE_TYPE) {
                g_vision_packet_buffer[packet_index++] = received_byte;
                parse_state = PARSE_GET_DATA;
            } else {
                // 无效类型，重置状态
                parse_state = PARSE_IDLE;
                packet_index = 0;
            }
            break;
            
        case PARSE_GET_DATA:
            // 获取数据内容
            if(received_byte == '#') {
                end_flag_count++;
                if(end_flag_count >= 2) {
                    // 找到结束标志 ##，开始解析
                    g_vision_packet_buffer[packet_index] = '\0';
                    Vision_Parse_Data_Packet(g_vision_packet_buffer);
                    
                    // 重置状态
                    parse_state = PARSE_IDLE;
                    packet_index = 0;
                    end_flag_count = 0;
                }
            } else {
                end_flag_count = 0;
                if(packet_index < VISION_BUFFER_SIZE - 1) {
                    g_vision_packet_buffer[packet_index++] = received_byte;
                }
            }
            break;
            
        default:
            parse_state = PARSE_IDLE;
            packet_index = 0;
            break;
    }
    
    // 重新启动串口接收
    HAL_UART_Receive_IT(&huart6, &g_vision_uart_rx_byte, 1);
}

/**
 * @brief 解析数据包主函数
 * @param packet_str 数据包字符串
 * @return 解析结果 (1: 成功, 0: 失败)
 */
uint8_t Vision_Parse_Data_Packet(char* packet_str)
{
    if(packet_str == NULL || strlen(packet_str) < 2) {
        g_vision_all_data.parse_error_count++;
        return 0;
    }
    
    char packet_type = packet_str[0];
    char* data_content = &packet_str[2];  // 跳过类型和逗号
    uint8_t parse_result = 0;
    
    my_printf(&huart1, "收到数据包类型: %c, 内容: %s\r\n", packet_type, data_content);
    
    switch(packet_type)
    {
        case VISION_TARGET_TYPE:    // 目标点数据包 $$T,150,95##
            parse_result = Vision_Parse_Target_Packet(data_content);
            break;
            
        case VISION_RECT_TYPE:      // 矩形数据包 $$R,cx,cy##
            parse_result = Vision_Parse_Rect_Packet(data_content);
            break;
            
        case VISION_CIRCLE_TYPE:    // 圆形数据包 $$C,count,x1,y1,...##
            parse_result = Vision_Parse_Circle_Packet(data_content);
            break;
            
        default:
            my_printf(&huart1, "未知数据包类型: %c\r\n", packet_type);
            g_vision_all_data.parse_error_count++;
            return 0;
    }
    
    if(parse_result) {
        g_vision_all_data.total_packets_received++;
        my_printf(&huart1, "数据包解析成功，总接收: %lu 包\r\n", g_vision_all_data.total_packets_received);
    } else {
        g_vision_all_data.parse_error_count++;
        my_printf(&huart1, "数据包解析失败，错误计数: %lu\r\n", g_vision_all_data.parse_error_count);
    }
    
    return parse_result;
}

/**
 * @brief 解析目标点数据包 格式: T,150,95
 * @param data_str 数据字符串
 * @return 解析结果 (1: 成功, 0: 失败)
 */
uint8_t Vision_Parse_Target_Packet(char* data_str)
{
    int x_coord, y_coord;
    
    if(sscanf(data_str, "%d,%d", &x_coord, &y_coord) == 2) {
        g_vision_all_data.target.x = (int16_t)x_coord;
        g_vision_all_data.target.y = (int16_t)y_coord;
        g_vision_all_data.target.is_valid = 1;
        g_vision_all_data.target.timestamp = HAL_GetTick();
        
        my_printf(&huart1, "目标点数据更新: X=%d, Y=%d\r\n", x_coord, y_coord);
        return 1;
    }
    
    my_printf(&huart1, "目标点数据解析失败: %s\r\n", data_str);
    return 0;
}

/**
 * @brief 解析矩形数据包 格式: R,cx,cy
 * @param data_str 数据字符串
 * @return 解析结果 (1: 成功, 0: 失败)
 */
uint8_t Vision_Parse_Rect_Packet(char* data_str)
{
    int center_x, center_y;
    
    if(sscanf(data_str, "%d,%d", &center_x, &center_y) == 2) {
        g_vision_all_data.rect.center_x = (int16_t)center_x;
        g_vision_all_data.rect.center_y = (int16_t)center_y;
        g_vision_all_data.rect.is_valid = 1;
        g_vision_all_data.rect.timestamp = HAL_GetTick();
        
        my_printf(&huart1, "矩形数据更新: 中心X=%d, 中心Y=%d\r\n", center_x, center_y);
        return 1;
    }
    
    my_printf(&huart1, "矩形数据解析失败: %s\r\n", data_str);
    return 0;
}
