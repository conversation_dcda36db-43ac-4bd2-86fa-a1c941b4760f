/**
 * @file vision_data.c
 * @brief 视觉模块数据处理实现文件
 * <AUTHOR> Code
 */

#include "vision_data.h"
#include "MyDefine.h"

/* 全局变量定义 */
Vision_Data_t g_vision_data = {0};                     // 全局视觉数据
uint8_t g_vision_rx_buffer[VISION_BUFFER_SIZE] = {0};  // 串口接收缓冲区
uint16_t g_vision_rx_index = 0;                        // 接收缓冲区索引
static Vision_Parse_State_t parse_state = VISION_STATE_IDLE; // 解析状态
static char packet_buffer[VISION_BUFFER_SIZE] = {0};   // 数据包缓冲区
static uint16_t packet_index = 0;                      // 数据包索引

/**
 * @brief 视觉模块初始化
 */
void Vision_Init(void)
{
    // 清除所有数据
    Vision_Clear_Data();
    
    // 重置解析状态
    parse_state = VISION_STATE_IDLE;
    g_vision_rx_index = 0;
    packet_index = 0;
    
    // 启动串口6中断接收
    HAL_UART_Receive_IT(&huart6, &g_vision_rx_buffer[0], 1);
    
    my_printf(&huart1, "视觉模块初始化完成，等待数据...\r\n");
}

/**
 * @brief 串口6接收中断回调函数
 * @param data 接收到的字节数据
 */
void Vision_UART_RxCallback(uint8_t data)
{
    static uint8_t start_count = 0;  // 开始标志计数
    static uint8_t end_count = 0;    // 结束标志计数
    
    switch(parse_state)
    {
        case VISION_STATE_IDLE:
            if(data == '$') {
                start_count++;
                if(start_count >= 2) {  // 检测到 $$
                    parse_state = VISION_STATE_TYPE;
                    packet_index = 0;
                    start_count = 0;
                }
            } else {
                start_count = 0;
            }
            break;
            
        case VISION_STATE_TYPE:
            if(data == 'T' || data == 'R' || data == 'C') {
                packet_buffer[packet_index++] = data;
                parse_state = VISION_STATE_DATA;
            } else {
                parse_state = VISION_STATE_IDLE;
                packet_index = 0;
            }
            break;
            
        case VISION_STATE_DATA:
            if(data == '#') {
                end_count++;
                if(end_count >= 2) {  // 检测到 ##
                    packet_buffer[packet_index] = '\0';  // 字符串结束
                    Vision_Parse_Packet(packet_buffer);   // 解析数据包
                    parse_state = VISION_STATE_IDLE;
                    packet_index = 0;
                    end_count = 0;
                }
            } else {
                end_count = 0;
                if(packet_index < VISION_BUFFER_SIZE - 1) {
                    packet_buffer[packet_index++] = data;
                }
            }
            break;
            
        default:
            parse_state = VISION_STATE_IDLE;
            packet_index = 0;
            break;
    }
    
    // 重新启动接收
    HAL_UART_Receive_IT(&huart6, &g_vision_rx_buffer[0], 1);
}

/**
 * @brief 解析数据包
 * @param packet 数据包字符串
 * @return 解析结果 (1: 成功, 0: 失败)
 */
uint8_t Vision_Parse_Packet(char* packet)
{
    if(packet == NULL || strlen(packet) < 2) {
        return 0;
    }
    
    char packet_type = packet[0];
    char* data_part = &packet[2];  // 跳过类型和逗号
    
    my_printf(&huart1, "收到数据包: %c, 数据: %s\r\n", packet_type, data_part);
    
    switch(packet_type)
    {
        case VISION_PACKET_TARGET:  // 目标点数据包
            return Vision_Parse_Target(data_part);
            
        case VISION_PACKET_RECT:    // 矩形数据包
            return Vision_Parse_Rect(data_part);
            
        case VISION_PACKET_CIRCLE:  // 圆形数据包
            return Vision_Parse_Circle(data_part);
            
        default:
            my_printf(&huart1, "未知数据包类型: %c\r\n", packet_type);
            return 0;
    }
}

/**
 * @brief 解析目标点数据 格式: $$T,150,95##
 * @param data 数据字符串
 * @return 解析结果 (1: 成功, 0: 失败)
 */
uint8_t Vision_Parse_Target(char* data)
{
    int x, y;
    
    if(sscanf(data, "%d,%d", &x, &y) == 2) {
        g_vision_data.target.x = (int16_t)x;
        g_vision_data.target.y = (int16_t)y;
        g_vision_data.target.valid = 1;
        g_vision_data.last_update_time = HAL_GetTick();
        
        my_printf(&huart1, "目标点数据更新: X=%d, Y=%d\r\n", x, y);
        return 1;
    }
    
    my_printf(&huart1, "目标点数据解析失败: %s\r\n", data);
    return 0;
}

/**
 * @brief 解析矩形数据 格式: $$R,cx,cy##
 * @param data 数据字符串
 * @return 解析结果 (1: 成功, 0: 失败)
 */
uint8_t Vision_Parse_Rect(char* data)
{
    int cx, cy;
    
    if(sscanf(data, "%d,%d", &cx, &cy) == 2) {
        g_vision_data.rect.cx = (int16_t)cx;
        g_vision_data.rect.cy = (int16_t)cy;
        g_vision_data.rect.valid = 1;
        g_vision_data.last_update_time = HAL_GetTick();
        
        my_printf(&huart1, "矩形数据更新: CX=%d, CY=%d\r\n", cx, cy);
        return 1;
    }
    
    my_printf(&huart1, "矩形数据解析失败: %s\r\n", data);
    return 0;
}

/**
 * @brief 解析圆形数据 格式: $$C,count,x1,y1,x2,y2,...##
 * @param data 数据字符串
 * @return 解析结果 (1: 成功, 0: 失败)
 */
uint8_t Vision_Parse_Circle(char* data)
{
    char* token;
    char data_copy[VISION_BUFFER_SIZE];
    int values[50];  // 临时存储解析的数值
    int value_count = 0;

    // 复制数据字符串，因为strtok会修改原字符串
    strncpy(data_copy, data, sizeof(data_copy) - 1);
    data_copy[sizeof(data_copy) - 1] = '\0';

    // 使用strtok分割字符串
    token = strtok(data_copy, ",");
    while(token != NULL && value_count < 50) {
        values[value_count++] = atoi(token);
        token = strtok(NULL, ",");
    }

    // 检查数据格式：至少要有count + 2*count个数值
    if(value_count < 1) {
        my_printf(&huart1, "圆形数据格式错误: 缺少count\r\n");
        return 0;
    }

    int count = values[0];
    if(count > VISION_MAX_CIRCLE_POINTS) {
        my_printf(&huart1, "圆形坐标点数量超限: %d > %d\r\n", count, VISION_MAX_CIRCLE_POINTS);
        count = VISION_MAX_CIRCLE_POINTS;  // 限制最大数量
    }

    if(value_count < (1 + 2 * count)) {
        my_printf(&huart1, "圆形数据不完整: 需要%d个数值，实际%d个\r\n", 1 + 2 * count, value_count);
        return 0;
    }

    // 解析坐标点
    g_vision_data.circle.count = count;
    for(int i = 0; i < count; i++) {
        g_vision_data.circle.points[i].x = (int16_t)values[1 + i * 2];
        g_vision_data.circle.points[i].y = (int16_t)values[1 + i * 2 + 1];
    }

    g_vision_data.circle.valid = 1;
    g_vision_data.last_update_time = HAL_GetTick();

    my_printf(&huart1, "圆形数据更新: 坐标点数=%d\r\n", count);
    for(int i = 0; i < count; i++) {
        my_printf(&huart1, "  点%d: X=%d, Y=%d\r\n", i+1,
                 g_vision_data.circle.points[i].x,
                 g_vision_data.circle.points[i].y);
    }

    return 1;
}

/**
 * @brief 打印所有视觉数据
 */
void Vision_Print_Data(void)
{
    my_printf(&huart1, "=== 视觉数据状态 ===\r\n");

    // 目标点数据
    if(g_vision_data.target.valid) {
        my_printf(&huart1, "目标点: X=%d, Y=%d\r\n",
                 g_vision_data.target.x, g_vision_data.target.y);
    } else {
        my_printf(&huart1, "目标点: 无效\r\n");
    }

    // 矩形数据
    if(g_vision_data.rect.valid) {
        my_printf(&huart1, "矩形中心: CX=%d, CY=%d\r\n",
                 g_vision_data.rect.cx, g_vision_data.rect.cy);
    } else {
        my_printf(&huart1, "矩形数据: 无效\r\n");
    }

    // 圆形数据
    if(g_vision_data.circle.valid) {
        my_printf(&huart1, "圆形坐标点数: %d\r\n", g_vision_data.circle.count);
        for(int i = 0; i < g_vision_data.circle.count; i++) {
            my_printf(&huart1, "  点%d: (%d, %d)\r\n", i+1,
                     g_vision_data.circle.points[i].x,
                     g_vision_data.circle.points[i].y);
        }
    } else {
        my_printf(&huart1, "圆形数据: 无效\r\n");
    }

    my_printf(&huart1, "最后更新时间: %lu ms\r\n", g_vision_data.last_update_time);
    my_printf(&huart1, "==================\r\n");
}

/**
 * @brief 清除所有视觉数据
 */
void Vision_Clear_Data(void)
{
    memset(&g_vision_data, 0, sizeof(Vision_Data_t));
    my_printf(&huart1, "视觉数据已清除\r\n");
}

/**
 * @brief 检查数据是否有效
 * @return 数据有效性 (1: 有效, 0: 无效)
 */
uint8_t Vision_Is_Data_Valid(void)
{
    uint32_t current_time = HAL_GetTick();
    uint32_t timeout = 5000;  // 5秒超时

    // 检查数据是否超时
    if(g_vision_data.last_update_time == 0 ||
       (current_time - g_vision_data.last_update_time) > timeout) {
        return 0;
    }

    // 检查至少有一种数据有效
    return (g_vision_data.target.valid ||
            g_vision_data.rect.valid ||
            g_vision_data.circle.valid);
}

/**
 * @brief 获取目标点数据指针
 * @return 目标点数据指针
 */
Vision_Target_t* Vision_Get_Target_Data(void)
{
    if(g_vision_data.target.valid) {
        return &g_vision_data.target;
    }
    return NULL;
}

/**
 * @brief 获取矩形数据指针
 * @return 矩形数据指针
 */
Vision_Rect_t* Vision_Get_Rect_Data(void)
{
    if(g_vision_data.rect.valid) {
        return &g_vision_data.rect;
    }
    return NULL;
}

/**
 * @brief 获取圆形数据指针
 * @return 圆形数据指针
 */
Vision_Circle_t* Vision_Get_Circle_Data(void)
{
    if(g_vision_data.circle.valid) {
        return &g_vision_data.circle;
    }
    return NULL;
}

/* ========== 串口6中断处理函数 ========== */

/**
 * @brief 串口6接收完成中断回调函数
 * @param huart 串口句柄
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if(huart->Instance == USART6) {
        // 处理接收到的数据
        Vision_UART_RxCallback(g_vision_rx_buffer[0]);
    }
}
