#ifndef __PID_APP_H__
#define __PID_APP_H__

#include "MyDefine.h"
extern int error_x;
extern int error_y;
// PID参数结构体
typedef struct
{
    float kp;          // 比例系数
    float ki;          // 积分系数
    float kd;          // 微分系数
    float out_min;     // 输出最小值
    float out_max;     // 输出最大值
} PidParams_t;

extern bool pid_running;
void PID_Init(void);
//void PID_angle_word(void);
void PID_Task(void);

#endif
