#include "pid_app.h"

/* PID 控制器实例 */
PID_T pid_speed_x;  // 左轮速度环
PID_T pid_speed_y; // 右轮速度环
//PID_T pid_angle;
int targe_error=0;
PidParams_t pid_params_X = {
    .kp = 4.2f,  // Calculated for balanced response to steps like 88 to 149
    .ki = 0.0f, // Lowered to minimize integral windup during long settles
    .kd = 0.0f,  // For controlling oscillations in transitions
    .out_min = -100.0f,
    .out_max = 100.0f
};

PidParams_t pid_params_Y = {
    .kp =5.2f,  // Calculated for balanced response to steps like 88 to 149
    .ki = 0.00f, // Lowered to minimize integral windup during long settles
    .kd = 0.01f,  // For controlling oscillations in transitions
    .out_min = -100.0f,
    .out_max = 100.0f
};
//PidParams_t pid_angle_s = {
//    .kp =4.0f,  // Calculated for balanced response to steps like 88 to 149
//    .ki = 0.0f, // Lowered to minimize integral windup during long settles
//    .kd = 0.0f,  // For controlling oscillations in transitions
//    .out_min = -100.0f,
//    .out_max = 100.0f
//};
void PID_Init(void)
{
  pid_init(&pid_speed_x,
           pid_params_X.kp, pid_params_X.ki, pid_params_X.kd,
           0.0f, pid_params_X.out_max);

  pid_init(&pid_speed_y,
           pid_params_Y.kp, pid_params_Y.ki, pid_params_Y.kd,
           0.0f, pid_params_Y.out_max);
//	pid_init(&pid_angle,
//           pid_angle_s.kp, pid_angle_s.ki, pid_angle_s.kd,
//           0.0f, pid_angle_s.out_max);
  pid_set_target(&pid_speed_x, 0);
  pid_set_target(&pid_speed_y, 0);
//	pid_set_target(&pid_angle, -90);//角度换
}
int error_x;//latest_green_laser_coord.x-latest_red_laser_coord.x
int error_y;//latest_green_laser_coord.y-latest_red_laser_coord.y
bool pid_running = false; // PID 控制使能开关true
int output_x, output_y;
int targe_error1=0;
int angle_up;
//void PID_angle_word(void)
//{
//angle_up=pid_calculate_positional(&pid_angle,yaw_val-90);
//angle_up = pid_constrain(angle_up, pid_angle_s.out_min,pid_angle_s.out_max);
//	StepMotor_Set_Speed(angle_up,0);


//}
void PID_Task(void)
{
	error_x=target_x-rect_cx;
	error_y=target_y-rect_cy;
  if(pid_running == false) return;
//    output_left = pid_calculate_incremental(&pid_speed_left, filtered_speed_left);
//    output_right = pid_calculate_incremental(&pid_speed_right, filtered_speed_right);
	output_x = pid_calculate_positional(&pid_speed_x, error_x);
  output_y = pid_calculate_positional(&pid_speed_y, error_y);
//		//output_yaw =  pid_calculate_incremental(&pid_yaw, yaw); -> PID计算 YAW -> 反馈

		//pid_set_target(&pid_speed_right,v - output_yaw);
	output_x = pid_constrain(output_x, pid_params_X.out_min,pid_params_X.out_max);
  output_y = pid_constrain(output_y, pid_params_Y.out_min,pid_params_Y.out_max);

//    // 设置电机速度
//    Motor_SetSpeed(&left_motor, output_left);
//    Motor_SetSpeed(&right_motor, output_right);
	 StepMotor_Set_Speed(-output_x,-output_y);
   my_printf(&huart1,"{targe}%d,%d\r\n", targe_error1, error_x);
//	my_printf(&huart1,"{me}%d,%d\r\n", error_y, error_x);
//	my_printf(&huart1,"{me1}%d\r\n", output_y);
//	my_printf(&huart1,"{right_filtered}%.2f,%.2f\r\n", pid_speed_right.target, filtered_speed_right);
}


