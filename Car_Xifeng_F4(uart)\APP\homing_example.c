/**
 * @file homing_example.c
 * @brief 步进电机回零功能使用示例
 * <AUTHOR> Code
 */
 
#include "StepMotor_app.h"

/**
 * @brief 回零功能使用示例1：基本回零操作
 */
void Homing_Example_Basic(void)
{
    my_printf(&huart1, "=== Basic Homing Example ===\r\n");
    
    // 1. 初始化步进电机系统
    StepMotor_Init();
    
    // 2. 设置当前位置为原点（不保存到Flash）
    StepMotor_Set_Current_As_Origin(0, false);  // 0表示所有轴
    
    // 3. 单轴回零 - X轴使用限位开关模式
    StepMotor_Home_Single_Motor(1, HOME_MODE_LIMIT_SWITCH, true);
    
    // 4. 单轴回零 - Y轴使用编码器Z相模式
    StepMotor_Home_Single_Motor(2, HOME_MODE_ENCODER_Z, true);
    
    my_printf(&huart1, "=== Basic Homing Complete ===\r\n");
}

/**
 * @brief 回零功能使用示例2：双轴同时回零
 */
void Homing_Example_Simultaneous(void)
{
    my_printf(&huart1, "=== Simultaneous Homing Example ===\r\n");
    
    // 1. 初始化步进电机系统
    StepMotor_Init();
    
    // 2. 双轴同时回零，使用堵转检测模式
    StepMotor_Home_All_Motors(HOME_MODE_STALL_DETECT, true);
    
    // 3. 回零完成后，设置当前位置为原点并保存到Flash
    StepMotor_Set_Current_As_Origin(0, true);  // 保存到Flash
    
    my_printf(&huart1, "=== Simultaneous Homing Complete ===\r\n");
}

/**
 * @brief 回零功能使用示例3：带超时的安全回零
 */
void Homing_Example_With_Timeout(void)
{
    my_printf(&huart1, "=== Safe Homing with Timeout Example ===\r\n");
    
    // 1. 初始化步进电机系统
    StepMotor_Init();
    
    // 2. X轴带超时回零（10秒超时）
    uint8_t x_result = StepMotor_Home_With_Timeout(1, HOME_MODE_LIMIT_SWITCH, 10000);
    
    if(x_result == HOME_STATUS_SUCCESS) {
        my_printf(&huart1, "X-Axis homing successful!\r\n");
        
        // 3. Y轴带超时回零（10秒超时）
        uint8_t y_result = StepMotor_Home_With_Timeout(2, HOME_MODE_LIMIT_SWITCH, 10000);
        
        if(y_result == HOME_STATUS_SUCCESS) {
            my_printf(&huart1, "Y-Axis homing successful!\r\n");
            // 两轴都成功回零，设置原点
            StepMotor_Set_Current_As_Origin(0, true);
        } else {
            my_printf(&huart1, "Y-Axis homing failed with status: %d\r\n", y_result);
        }
    } else {
        my_printf(&huart1, "X-Axis homing failed with status: %d\r\n", x_result);
    }
    
    my_printf(&huart1, "=== Safe Homing Complete ===\r\n");
}

/**
 * @brief 回零功能使用示例4：紧急停止回零
 */
void Homing_Example_Emergency_Stop(void)
{
    my_printf(&huart1, "=== Emergency Stop Homing Example ===\r\n");
    
    // 1. 初始化步进电机系统
    StepMotor_Init();
    
    // 2. 启动回零运动（不等待完成）
    StepMotor_Home_Single_Motor(1, HOME_MODE_LIMIT_SWITCH, false);
    StepMotor_Home_Single_Motor(2, HOME_MODE_LIMIT_SWITCH, false);
    
    // 3. 模拟运行一段时间后需要紧急停止
    HAL_Delay(1000);  // 运行1秒
    
    // 4. 紧急停止所有轴的回零运动
    StepMotor_Interrupt_Homing(0);  // 0表示所有轴
    
    my_printf(&huart1, "=== Emergency Stop Complete ===\r\n");
}

/**
 * @brief 回零功能使用示例5：位置重置
 */
void Homing_Example_Position_Reset(void)
{
    my_printf(&huart1, "=== Position Reset Example ===\r\n");
    
    // 1. 初始化步进电机系统
    StepMotor_Init();
    
    // 2. 移动到某个位置
    StepMotor_Move_Pulses(1000, 500);  // X轴1000脉冲，Y轴500脉冲
    HAL_Delay(2000);  // 等待运动完成
    
    // 3. 打印当前位置信息
    StepMotor_Print_Pulse_Status();
    
    // 4. 将当前位置重置为零点（软件清零，不进行物理运动）
    StepMotor_Reset_Position_To_Zero(0);  // 0表示所有轴
    
    // 5. 再次打印位置信息，确认已清零
    StepMotor_Print_Pulse_Status();
    
    my_printf(&huart1, "=== Position Reset Complete ===\r\n");
}

/**
 * @brief 完整的回零流程示例
 */
void Homing_Example_Complete_Sequence(void)
{
    my_printf(&huart1, "=== Complete Homing Sequence ===\r\n");

    // 1. 系统初始化
    StepMotor_Init();

    // 2. 先进行安全的带超时回零
    my_printf(&huart1, "Step 1: Safe homing with timeout...\r\n");
    uint8_t x_result = StepMotor_Home_With_Timeout(1, HOME_MODE_LIMIT_SWITCH, 15000);
    uint8_t y_result = StepMotor_Home_With_Timeout(2, HOME_MODE_LIMIT_SWITCH, 15000);

    // 3. 检查回零结果
    if(x_result == HOME_STATUS_SUCCESS && y_result == HOME_STATUS_SUCCESS) {
        my_printf(&huart1, "Step 2: Both axes homed successfully\r\n");

        // 4. 设置原点并保存
        StepMotor_Set_Current_As_Origin(0, true);

        // 5. 验证：移动一段距离后再回零
        my_printf(&huart1, "Step 3: Testing movement and return to origin...\r\n");
        StepMotor_Move_Pulses(2000, 1500);
        HAL_Delay(3000);

        // 6. 再次回零验证
        StepMotor_Home_All_Motors(HOME_MODE_LIMIT_SWITCH, true);

        my_printf(&huart1, "=== Complete Homing Sequence SUCCESS ===\r\n");
    } else {
        my_printf(&huart1, "=== Homing FAILED - X:%d, Y:%d ===\r\n", x_result, y_result);
    }
}

/**
 * @brief 智能回零示例 - 推荐使用！
 * @note 这是最简单的使用方式，自动处理首次设置和后续回零
 */
void Homing_Example_Smart_Sequence(void)
{
    my_printf(&huart1, "=== Smart Homing Example (RECOMMENDED) ===\r\n");

    // 1. 系统初始化
    StepMotor_Init();

    // 2. 智能回零序列 - 一个函数搞定所有！
    StepMotor_Smart_Home_Sequence();

    // 3. 测试移动
    my_printf(&huart1, "Testing movement...\r\n");
    StepMotor_Move_Pulses(1000, 800);
    HAL_Delay(2000);

    // 4. 再次回零（这次会直接返回到保存的原点）
    my_printf(&huart1, "Returning to origin again...\r\n");
    StepMotor_Return_To_Origin(0, true);

    my_printf(&huart1, "=== Smart Homing Complete ===\r\n");
}

/**
 * @brief 原点状态检查示例
 */
void Homing_Example_Check_Status(void)
{
    my_printf(&huart1, "=== Origin Status Check Example ===\r\n");

    // 检查各轴原点状态
    uint8_t x_status = StepMotor_Check_Origin_Status(1);
    uint8_t y_status = StepMotor_Check_Origin_Status(2);
    uint8_t all_status = StepMotor_Check_Origin_Status(0);

    my_printf(&huart1, "X-Axis Origin: %s\r\n", x_status ? "SET" : "NOT SET");
    my_printf(&huart1, "Y-Axis Origin: %s\r\n", y_status ? "SET" : "NOT SET");

    switch(all_status) {
        case 0:
            my_printf(&huart1, "Overall Status: No origins set\r\n");
            break;
        case 1:
            my_printf(&huart1, "Overall Status: All origins set\r\n");
            break;
        case 2:
            my_printf(&huart1, "Overall Status: Partial setup\r\n");
            break;
    }

    // 根据状态执行相应操作
    if(all_status == 1) {
        my_printf(&huart1, "Origins are set, can use StepMotor_Return_To_Origin()\r\n");
        StepMotor_Return_To_Origin(0, true);
    } else {
        my_printf(&huart1, "Need to setup origins first\r\n");
        StepMotor_Setup_Origin_Once(0, HOME_MODE_LIMIT_SWITCH);
    }
}

/**
 * @brief 典型应用场景示例 - 设备启动时的回零流程
 */
void Homing_Example_Device_Startup(void)
{
    my_printf(&huart1, "=== Device Startup Homing ===\r\n");

    // 1. 系统初始化
    StepMotor_Init();

    // 2. 设备启动时，使用智能回零
    // 第一次使用：会进行完整的回零设置并保存到Flash
    // 后续使用：直接返回到保存的原点位置
    StepMotor_Smart_Home_Sequence();

    // 3. 现在可以正常使用设备了
    my_printf(&huart1, "Device ready for normal operation!\r\n");

    // 4. 在程序运行过程中，如果需要回零，直接调用：
    // StepMotor_Return_To_Origin(0, true);  // 返回到保存的原点
}

/**
 * @brief 生产环境推荐用法
 */
void Homing_Example_Production_Usage(void)
{
    my_printf(&huart1, "=== Production Environment Usage ===\r\n");

    StepMotor_Init();

    // 生产环境中，推荐的使用方式：

    // 方式1：设备首次安装时，手动设置原点
    // StepMotor_Setup_Origin_Once(0, HOME_MODE_LIMIT_SWITCH);

    // 方式2：日常使用时，直接返回原点
    if(StepMotor_Check_Origin_Status(0) == 1) {
        my_printf(&huart1, "Origins already set, returning to origin...\r\n");
        StepMotor_Return_To_Origin(0, true);
    } else {
        my_printf(&huart1, "Origins not set, performing first-time setup...\r\n");
        StepMotor_Setup_Origin_Once(0, HOME_MODE_LIMIT_SWITCH);
    }

    // 方式3：最简单的方式，使用智能序列
    // StepMotor_Smart_Home_Sequence();  // 自动处理所有情况

    my_printf(&huart1, "=== Production Usage Complete ===\r\n");
}
