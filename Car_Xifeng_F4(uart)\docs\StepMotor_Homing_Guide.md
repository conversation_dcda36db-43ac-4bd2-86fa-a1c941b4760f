# 步进电机回零功能使用指南

## 📋 概述

本文档介绍了基于Emm_V5步进电机驱动器的回零功能封装，提供了完整的API接口和使用示例。

## 🔧 功能特性

### 1. 回零模式支持
- **限位开关回零** (`HOME_MODE_LIMIT_SWITCH = 0`)
- **编码器Z相回零** (`HOME_MODE_ENCODER_Z = 1`) 
- **堵转检测回零** (`HOME_MODE_STALL_DETECT = 2`)
- **手动停止回零** (`HOME_MODE_MANUAL_STOP = 3`)

### 2. 回零状态
- `HOME_STATUS_IDLE = 0` - 空闲状态
- `HOME_STATUS_HOMING = 1` - 正在回零
- `HOME_STATUS_SUCCESS = 2` - 回零成功
- `HOME_STATUS_TIMEOUT = 3` - 回零超时
- `HOME_STATUS_ERROR = 4` - 回零错误

## 📚 API函数说明

### 基础回零函数

#### 1. 设置当前位置为原点
```c
void StepMotor_Set_Current_As_Origin(uint8_t motor_id, bool save_to_flash);
```
- **参数**：
  - `motor_id`: 电机ID (1: X轴, 2: Y轴, 0: 所有轴)
  - `save_to_flash`: 是否保存到Flash (true: 保存, false: 不保存)
- **功能**：将当前位置设置为原点，并重置脉冲计数

#### 2. 位置重置为零点
```c
void StepMotor_Reset_Position_To_Zero(uint8_t motor_id);
```
- **参数**：
  - `motor_id`: 电机ID (1: X轴, 2: Y轴, 0: 所有轴)
- **功能**：软件清零当前位置，不进行物理运动

### 回零运动函数

#### 3. 单轴回零运动
```c
void StepMotor_Home_Single_Motor(uint8_t motor_id, uint8_t home_mode, bool wait_complete);
```
- **参数**：
  - `motor_id`: 电机ID (1: X轴, 2: Y轴)
  - `home_mode`: 回零模式 (0-3)
  - `wait_complete`: 是否等待完成
- **功能**：执行单轴回零运动

#### 4. 双轴同时回零
```c
void StepMotor_Home_All_Motors(uint8_t home_mode, bool wait_complete);
```
- **参数**：
  - `home_mode`: 回零模式 (0-3)
  - `wait_complete`: 是否等待完成
- **功能**：同时启动X轴和Y轴回零运动

#### 5. 带超时的回零运动
```c
uint8_t StepMotor_Home_With_Timeout(uint8_t motor_id, uint8_t home_mode, uint32_t timeout_ms);
```
- **参数**：
  - `motor_id`: 电机ID (1: X轴, 2: Y轴)
  - `home_mode`: 回零模式 (0-3)
  - `timeout_ms`: 超时时间(毫秒)
- **返回值**：回零状态 (HOME_STATUS_SUCCESS/TIMEOUT/ERROR)
- **功能**：执行带超时保护的回零运动

#### 6. 中断回零运动
```c
void StepMotor_Interrupt_Homing(uint8_t motor_id);
```
- **参数**：
  - `motor_id`: 电机ID (1: X轴, 2: Y轴, 0: 所有轴)
- **功能**：强制中断正在进行的回零运动

### 智能零点管理函数 ⭐

#### 7. 一次性设置原点（推荐）
```c
void StepMotor_Setup_Origin_Once(uint8_t motor_id, uint8_t home_mode);
```
- **参数**：
  - `motor_id`: 电机ID (1: X轴, 2: Y轴, 0: 所有轴)
  - `home_mode`: 回零模式 (0-3)
- **功能**：首次设置原点，自动保存到Flash，如果已设置则跳过

#### 8. 检查原点状态
```c
uint8_t StepMotor_Check_Origin_Status(uint8_t motor_id);
```
- **参数**：
  - `motor_id`: 电机ID (1: X轴, 2: Y轴, 0: 所有轴)
- **返回值**：1=已设置, 0=未设置, 2=部分设置
- **功能**：检查原点是否已经设置过

#### 9. 返回到原点
```c
void StepMotor_Return_To_Origin(uint8_t motor_id, bool wait_complete);
```
- **参数**：
  - `motor_id`: 电机ID (1: X轴, 2: Y轴, 0: 所有轴)
  - `wait_complete`: 是否等待完成
- **功能**：返回到已保存的原点位置（需要先设置过原点）

#### 10. 智能回零序列（最推荐）
```c
void StepMotor_Smart_Home_Sequence(void);
```
- **功能**：自动判断并执行合适的回零操作
  - 首次使用：执行完整回零设置并保存
  - 后续使用：直接返回到保存的原点

## 🎯 **重要概念：零点只需设置一次！**

### 零点设置的两种情况：

1. **首次使用**：需要进行物理回零运动，找到机械零点并保存到Flash
2. **后续使用**：直接返回到已保存的零点位置，无需重新设置

### 智能零点管理（推荐使用）：

```c
// 🌟 最简单的使用方式 - 一个函数搞定所有！
StepMotor_Init();
StepMotor_Smart_Home_Sequence();  // 自动判断是首次设置还是返回原点
```

## 💡 使用示例

### 示例1：智能回零（推荐）⭐
```c
// 初始化系统
StepMotor_Init();

// 智能回零序列：
// - 如果是首次使用：自动进行回零设置并保存到Flash
// - 如果已设置过：直接返回到保存的原点
StepMotor_Smart_Home_Sequence();

// 现在可以正常使用了！
```

### 示例2：手动控制零点设置
```c
// 检查原点是否已设置
if(StepMotor_Check_Origin_Status(0) == 1) {
    // 原点已设置，直接返回
    StepMotor_Return_To_Origin(0, true);
} else {
    // 首次使用，设置原点（只需要一次）
    StepMotor_Setup_Origin_Once(0, HOME_MODE_LIMIT_SWITCH);
}
```

### 示例3：传统方式（基本回零操作）
```c
// 初始化系统
StepMotor_Init();

// X轴限位开关回零
StepMotor_Home_Single_Motor(1, HOME_MODE_LIMIT_SWITCH, true);

// Y轴编码器Z相回零
StepMotor_Home_Single_Motor(2, HOME_MODE_ENCODER_Z, true);

// 设置当前位置为原点（只在首次使用时需要）
StepMotor_Set_Current_As_Origin(0, true);  // 保存到Flash
```

### 示例2：安全回零（带超时）
```c
// X轴回零，10秒超时
uint8_t result = StepMotor_Home_With_Timeout(1, HOME_MODE_LIMIT_SWITCH, 10000);

if(result == HOME_STATUS_SUCCESS) {
    my_printf(&huart1, "X轴回零成功!\r\n");
    StepMotor_Set_Current_As_Origin(1, false);
} else {
    my_printf(&huart1, "X轴回零失败，状态: %d\r\n", result);
}
```

### 示例3：双轴同时回零
```c
// 双轴同时回零，使用堵转检测
StepMotor_Home_All_Motors(HOME_MODE_STALL_DETECT, true);

// 设置原点并保存
StepMotor_Set_Current_As_Origin(0, true);
```

### 示例4：紧急停止
```c
// 启动回零（不等待）
StepMotor_Home_Single_Motor(1, HOME_MODE_LIMIT_SWITCH, false);

// 运行一段时间后紧急停止
HAL_Delay(1000);
StepMotor_Interrupt_Homing(1);
```

## ⚠️ 注意事项

1. **安全第一**：回零前确保机械结构安全，避免碰撞
2. **超时设置**：建议使用带超时的回零函数，防止死锁
3. **模式选择**：根据硬件配置选择合适的回零模式
4. **状态检查**：回零后检查返回状态，确保操作成功
5. **原点保存**：重要应用建议将原点位置保存到Flash

## 🔍 调试信息

所有回零函数都会通过UART1输出详细的调试信息，包括：
- 回零模式和参数
- 执行状态和进度
- 成功/失败结果
- 错误信息和建议

## 📁 相关文件

- `StepMotor_app.h` - 回零功能API声明
- `StepMotor_app.c` - 回零功能实现
- `homing_example.c` - 完整使用示例
- `Emm_V5.h/c` - 底层驱动库

## 🚀 快速开始

### 方式1：最简单的使用（推荐）⭐
```c
#include "StepMotor_app.h"

int main(void) {
    StepMotor_Init();                    // 1. 初始化系统
    StepMotor_Smart_Home_Sequence();     // 2. 智能回零（一个函数搞定所有）

    // 现在可以正常使用设备了！
    // 后续如需回零，直接调用：
    // StepMotor_Return_To_Origin(0, true);
}
```

### 方式2：手动控制
```c
#include "StepMotor_app.h"

int main(void) {
    StepMotor_Init();                    // 1. 初始化系统

    // 2. 检查是否已设置原点
    if(StepMotor_Check_Origin_Status(0) == 1) {
        // 已设置，直接返回原点
        StepMotor_Return_To_Origin(0, true);
    } else {
        // 未设置，首次设置原点（只需要一次）
        StepMotor_Setup_Origin_Once(0, HOME_MODE_LIMIT_SWITCH);
    }
}
```

### 方式3：传统方式
```c
#include "StepMotor_app.h"

int main(void) {
    StepMotor_Init();                                           // 1. 初始化系统
    StepMotor_Home_With_Timeout(1, HOME_MODE_LIMIT_SWITCH, 10000); // 2. X轴回零
    StepMotor_Home_With_Timeout(2, HOME_MODE_LIMIT_SWITCH, 10000); // 3. Y轴回零
    StepMotor_Set_Current_As_Origin(0, true);                   // 4. 设置原点（首次使用）
}
```

## 🎯 **关键要点**

1. **零点只需设置一次**：使用`save_to_flash=true`保存到Flash，掉电不丢失
2. **推荐使用智能回零**：`StepMotor_Smart_Home_Sequence()`自动处理所有情况
3. **生产环境**：设备安装时设置一次原点，日常使用直接返回原点
4. **安全第一**：使用带超时的函数，避免机械碰撞

现在你可以轻松实现各种回零功能了！🎉
