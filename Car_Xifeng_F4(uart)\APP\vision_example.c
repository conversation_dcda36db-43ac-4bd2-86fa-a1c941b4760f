/**
 * @file vision_example.c
 * @brief 视觉模块使用示例
 * <AUTHOR> Code
 */

#include "vision_data.h"
#include "StepMotor_app.h"

/**
 * @brief 视觉模块基本使用示例
 */
void Vision_Example_Basic_Usage(void)
{
    my_printf(&huart1, "=== 视觉模块基本使用示例 ===\r\n");
    
    // 1. 初始化视觉模块
    Vision_Init();
    
    // 2. 等待数据接收
    my_printf(&huart1, "等待视觉数据...\r\n");
    
    uint32_t start_time = HAL_GetTick();
    while((HAL_GetTick() - start_time) < 10000) {  // 等待10秒
        
        // 检查是否有新数据
        if(Vision_Is_Data_Valid()) {
            my_printf(&huart1, "检测到有效数据！\r\n");
            Vision_Print_Data();  // 打印所有数据
            break;
        }
        
        HAL_Delay(100);
    }
    
    my_printf(&huart1, "=== 基本使用示例结束 ===\r\n");
}

/**
 * @brief 目标点跟踪示例
 */
void Vision_Example_Target_Tracking(void)
{
    my_printf(&huart1, "=== 目标点跟踪示例 ===\r\n");
    
    Vision_Init();
    StepMotor_Init();
    
    while(1) {
        // 获取目标点数据
        Vision_Target_t* target = Vision_Get_Target_Data();
        
        if(target != NULL) {
            my_printf(&huart1, "目标点: X=%d, Y=%d\r\n", target->x, target->y);
            
            // 将目标点坐标转换为电机脉冲数
            int32_t x_pulses = target->x * 10;  // 根据实际比例调整
            int32_t y_pulses = target->y * 10;  // 根据实际比例调整
            
            // 控制电机移动到目标位置
            my_printf(&huart1, "移动到目标位置: X=%ld, Y=%ld 脉冲\r\n", x_pulses, y_pulses);
            StepMotor_Move_Pulses(x_pulses, y_pulses);
            
            // 等待电机到位
            HAL_Delay(2000);
            
            // 清除目标数据，等待下一个目标
            g_vision_data.target.valid = 0;
        }
        
        HAL_Delay(100);
    }
}

/**
 * @brief 矩形中心定位示例
 */
void Vision_Example_Rect_Center(void)
{
    my_printf(&huart1, "=== 矩形中心定位示例 ===\r\n");
    
    Vision_Init();
    StepMotor_Init();
    
    while(1) {
        // 获取矩形数据
        Vision_Rect_t* rect = Vision_Get_Rect_Data();
        
        if(rect != NULL) {
            my_printf(&huart1, "矩形中心: CX=%d, CY=%d\r\n", rect->cx, rect->cy);
            
            // 移动到矩形中心
            int32_t x_pulses = rect->cx * 8;   // 根据实际比例调整
            int32_t y_pulses = rect->cy * 8;   // 根据实际比例调整
            
            my_printf(&huart1, "移动到矩形中心: X=%ld, Y=%ld 脉冲\r\n", x_pulses, y_pulses);
            StepMotor_Move_Pulses(x_pulses, y_pulses);
            
            HAL_Delay(3000);
            
            // 清除矩形数据
            g_vision_data.rect.valid = 0;
        }
        
        HAL_Delay(100);
    }
}

/**
 * @brief 圆形轮廓跟踪示例
 */
void Vision_Example_Circle_Tracking(void)
{
    my_printf(&huart1, "=== 圆形轮廓跟踪示例 ===\r\n");
    
    Vision_Init();
    StepMotor_Init();
    
    while(1) {
        // 获取圆形数据
        Vision_Circle_t* circle = Vision_Get_Circle_Data();
        
        if(circle != NULL) {
            my_printf(&huart1, "圆形轮廓: %d个坐标点\r\n", circle->count);
            
            // 按顺序访问每个坐标点
            for(int i = 0; i < circle->count; i++) {
                int16_t x = circle->points[i].x;
                int16_t y = circle->points[i].y;
                
                my_printf(&huart1, "移动到点%d: (%d, %d)\r\n", i+1, x, y);
                
                // 转换为电机脉冲
                int32_t x_pulses = x * 12;  // 根据实际比例调整
                int32_t y_pulses = y * 12;  // 根据实际比例调整
                
                StepMotor_Move_Pulses(x_pulses, y_pulses);
                HAL_Delay(1500);  // 在每个点停留1.5秒
            }
            
            my_printf(&huart1, "圆形轮廓跟踪完成\r\n");
            
            // 清除圆形数据
            g_vision_data.circle.valid = 0;
        }
        
        HAL_Delay(100);
    }
}

/**
 * @brief 综合处理示例
 */
void Vision_Example_Comprehensive(void)
{
    my_printf(&huart1, "=== 综合处理示例 ===\r\n");
    
    Vision_Init();
    StepMotor_Init();
    
    while(1) {
        // 优先处理目标点
        Vision_Target_t* target = Vision_Get_Target_Data();
        if(target != NULL) {
            my_printf(&huart1, "处理目标点: (%d, %d)\r\n", target->x, target->y);
            StepMotor_Move_Pulses(target->x * 10, target->y * 10);
            g_vision_data.target.valid = 0;  // 处理完成后清除
            HAL_Delay(2000);
            continue;
        }
        
        // 其次处理矩形
        Vision_Rect_t* rect = Vision_Get_Rect_Data();
        if(rect != NULL) {
            my_printf(&huart1, "处理矩形中心: (%d, %d)\r\n", rect->cx, rect->cy);
            StepMotor_Move_Pulses(rect->cx * 8, rect->cy * 8);
            g_vision_data.rect.valid = 0;  // 处理完成后清除
            HAL_Delay(2000);
            continue;
        }
        
        // 最后处理圆形
        Vision_Circle_t* circle = Vision_Get_Circle_Data();
        if(circle != NULL) {
            my_printf(&huart1, "处理圆形轮廓: %d个点\r\n", circle->count);
            
            // 只移动到第一个点作为示例
            if(circle->count > 0) {
                StepMotor_Move_Pulses(circle->points[0].x * 12, circle->points[0].y * 12);
            }
            
            g_vision_data.circle.valid = 0;  // 处理完成后清除
            HAL_Delay(2000);
            continue;
        }
        
        // 没有数据时的处理
        HAL_Delay(100);
    }
}

/**
 * @brief 数据监控示例
 */
void Vision_Example_Data_Monitor(void)
{
    my_printf(&huart1, "=== 数据监控示例 ===\r\n");
    
    Vision_Init();
    
    uint32_t last_print_time = 0;
    
    while(1) {
        uint32_t current_time = HAL_GetTick();
        
        // 每2秒打印一次数据状态
        if(current_time - last_print_time > 2000) {
            if(Vision_Is_Data_Valid()) {
                Vision_Print_Data();
            } else {
                my_printf(&huart1, "等待视觉数据...\r\n");
            }
            last_print_time = current_time;
        }
        
        HAL_Delay(100);
    }
}
