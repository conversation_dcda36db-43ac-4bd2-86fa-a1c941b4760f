#include "MyDefine.h"
#include "StepMotor_app.h"
#include <math.h>
#include <stdlib.h>

/* ========== 编码器全局变量定义 ========== */
uint8_t motor_x_rx_buffer[MOTOR_RX_BUFFER_SIZE] = {0};  // X轴编码器接收缓冲区
uint8_t motor_y_rx_buffer[MOTOR_RX_BUFFER_SIZE] = {0};  // Y轴编码器接收缓冲区
uint8_t motor_x_rx_state = 0;                           // X轴接收状态机状态
uint8_t motor_y_rx_state = 0;                           // Y轴接收状态机状态
uint8_t motor_x_rx_counter = 0;                         // X轴接收计数器
uint8_t motor_y_rx_counter = 0;                         // Y轴接收计数器
uint8_t motor_x_ready = 0;                              // X轴电机到位标志
uint8_t motor_y_ready = 0;                              // Y轴电机到位标志
uint8_t stop_flag_car = 0;                              // 系统状态标志
uint8_t motor_x_rx_byte = 0;                            // X轴单字节接收缓冲
uint8_t motor_y_rx_byte = 0;                            // Y轴单字节接收缓冲

/* ========== 脉冲计数全局变量定义 ========== */
int32_t motor_x_pulse_count = 0;                        // X轴当前脉冲计数
int32_t motor_y_pulse_count = 0;                        // Y轴当前脉冲计数
int32_t motor_x_target_pulses = 0;                      // X轴目标脉冲数
int32_t motor_y_target_pulses = 0;                      // Y轴目标脉冲数
void StepMotor_Move_Pulses_Speed(int32_t x_pulses, int32_t y_pulses, uint16_t speed_rpm)
{
    uint8_t x_dir, y_dir;
    uint32_t x_pulse_abs, y_pulse_abs;

    /* 计算X轴方向和脉冲数 */
    if (x_pulses >= 0) {
        x_dir = 0;               /* CW方向 */
        x_pulse_abs = (uint32_t)x_pulses;
    } else {
        x_dir = 1;               /* CCW方向 */
        x_pulse_abs = (uint32_t)(-x_pulses);
    }

    /* 计算Y轴方向和脉冲数 */
    if (y_pulses >= 0) {
        y_dir = 0;               /* CW方向 */
        y_pulse_abs = (uint32_t)y_pulses;
    } else {
        y_dir = 1;               /* CCW方向 */
        y_pulse_abs = (uint32_t)(-y_pulses);
    }

    /* 更新目标脉冲数 */
    motor_x_target_pulses = x_pulses;
    motor_y_target_pulses = y_pulses;

    /* 使用指定速度运行位置模式。保持加速度为宏定义 MOTOR_ACCEL，
       raF=false 表示相对运动，snF 使用全局宏 MOTOR_SYNC_FLAG。 */
    Emm_V5_Pos_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, speed_rpm, MOTOR_ACCEL,
                       y_pulse_abs, false, MOTOR_SYNC_FLAG);
    Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, speed_rpm, MOTOR_ACCEL,
                       x_pulse_abs, false, MOTOR_SYNC_FLAG);

    /* 更新当前脉冲计数 */
    motor_x_pulse_count += x_pulses;
    motor_y_pulse_count += y_pulses;
}

/**
 * @brief 控制XY轴电机转动固定的脉冲数
 * @param x_pulses X轴脉冲数，范围可正可负
 * @param y_pulses Y轴脉冲数，范围可正可负
 */
void StepMotor_Move_Pulses(int32_t x_pulses, int32_t y_pulses)
{
    uint8_t x_dir, y_dir;
    uint32_t x_pulse_abs, y_pulse_abs;

    /* 计算X轴方向和脉冲数 */
    if (x_pulses >= 0)
    {
        x_dir = 0; /* CW方向 */
        x_pulse_abs = (uint32_t)x_pulses;
    }
    else
    {
        x_dir = 1; /* CCW方向 */
        x_pulse_abs = (uint32_t)(-x_pulses);
    }

    /* 计算Y轴方向和脉冲数 */
    if (y_pulses >= 0)
    {
        y_dir = 0; /* CW方向 */
        y_pulse_abs = (uint32_t)y_pulses;
    }
    else
    {
        y_dir = 1; /* CCW方向 */
        y_pulse_abs = (uint32_t)(-y_pulses);
    }

    /* 更新目标脉冲数 */
    motor_x_target_pulses = x_pulses;
    motor_y_target_pulses = y_pulses;

    /* 控制Y轴电机进行脉冲运动 - 这里是问题所在，Y和X反了 */
    Emm_V5_Pos_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, MOTOR_MAX_SPEED, MOTOR_ACCEL, y_pulse_abs, false, MOTOR_SYNC_FLAG);

    /* 控制X轴电机进行脉冲运动 - 这里是问题所在，X和Y反了 */
    Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, MOTOR_MAX_SPEED, MOTOR_ACCEL, x_pulse_abs, false, MOTOR_SYNC_FLAG);

    /* 更新当前脉冲计数 */
    motor_x_pulse_count += x_pulses;
    motor_y_pulse_count += y_pulses;
}
/**
 * @brief �����ʼ������
 */
void StepMotor_Init(void)
{
    /* ʹ��X���� */
    Emm_V5_En_Control(&MOTOR_X_UART, MOTOR_X_ADDR, true, MOTOR_SYNC_FLAG);

    /* ʹ��Y���� */
    Emm_V5_En_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, true, MOTOR_SYNC_FLAG);

    /* ��ʼֹͣ */
    StepMotor_Stop();

    /* ========== 编码器初始化 ========== */
    StepMotor_Clear_Ready_Flags();                          // 清除编码器到位标志
    HAL_UART_Receive_IT(&MOTOR_X_UART, &motor_x_rx_byte, 1); // 启动X轴编码器中断接收
    HAL_UART_Receive_IT(&MOTOR_Y_UART, &motor_y_rx_byte, 1); // 启动Y轴编码器中断接收
    my_printf(&huart1, "StepMotor+Encoder Init OK\r\n");
}

/**
 * @brief ����XY�����ٶ�
 * @param x_percent X���ٶȰٷֱȣ���Χ-100��100
 * @param y_percent Y���ٶȰٷֱȣ���Χ-100��100
 */
void StepMotor_Set_Speed(int8_t x_percent, int8_t y_percent)
{
    uint8_t x_dir, y_dir;
    uint16_t x_speed, y_speed;

    /* ���ưٷֱȷ�Χ */
    if (x_percent > 100)
        x_percent = 100;
    if (x_percent < -100)
        x_percent = -100;
    if (y_percent > 100)
        y_percent = 100;
    if (y_percent < -100)
        y_percent = -100;

    /* ����X�᷽�� */
    if (x_percent >= 0)
    {
        x_dir = 0; /* CW���� */
    }
    else
    {
        x_dir = 1;              /* CCW���� */
        x_percent = -x_percent; /* ȡ����ֵ */
    }

    /* ����Y�᷽�� */
    if (y_percent >= 0)
    {
        y_dir = 0; /* CW���� */
    }
    else
    {
        y_dir = 1;              /* CCW���� */
        y_percent = -y_percent; /* ȡ����ֵ */
    }

    /* ����ʵ���ٶ�ֵ(�ٷֱ�ת��ΪRPM) */
    x_speed = (uint16_t)((x_percent * MOTOR_MAX_SPEED) / 100);
    y_speed = (uint16_t)((y_percent * MOTOR_MAX_SPEED) / 100);

    /* ����X���� */
    Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, x_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);

    /* ����Y���� */
    Emm_V5_Vel_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, y_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);
}

/**
 * @brief ֹͣ���е��
 */
void StepMotor_Stop(void)
{
    /* ֹͣX���� */
    Emm_V5_Stop_Now(&MOTOR_X_UART, MOTOR_X_ADDR, MOTOR_SYNC_FLAG);

    /* ֹͣY���� */
    Emm_V5_Stop_Now(&MOTOR_Y_UART, MOTOR_Y_ADDR, MOTOR_SYNC_FLAG);
}

/* ========== 编码器数据处理函数 ========== */

/**
 * @brief X轴电机编码器数据处理函数
 * @param com_data 接收到的单字节数据
 */
void Motor_X_Receive_Data(uint8_t com_data)
{
    uint8_t i;

    // 状态0：等待起始字节(0x01)
    if (motor_x_rx_state == 0 && com_data == MOTOR_START_BYTE_01)
    {
        motor_x_rx_state = 1;                               // 进入数据接收状态
        motor_x_rx_buffer[motor_x_rx_counter++] = com_data; // 存储起始字节
    }
    // 状态1：接收数据字节
    else if (motor_x_rx_state == 1)
    {
        motor_x_rx_buffer[motor_x_rx_counter++] = com_data; // 存储数据字节

        // 如果缓冲区满或接收到结束字节0x6B，进入解析状态
        if (motor_x_rx_counter >= MOTOR_RX_BUFFER_SIZE || com_data == MOTOR_END_BYTE)
        {
            motor_x_rx_state = 2;
        }
    }

    // 状态2：解析接收到的数据
    if (motor_x_rx_state == 2)
    {
        // 检查最后一个字节是否为0x6B，验证数据完整性
        if (motor_x_rx_buffer[motor_x_rx_counter - 1] == MOTOR_END_BYTE)
        {
            // 检查0x01起始字节和到位标识字节0xFD 0x9F
            if (motor_x_rx_buffer[0] == MOTOR_START_BYTE_01 &&
                motor_x_rx_buffer[1] == MOTOR_READY_FLAG_1 &&
                motor_x_rx_buffer[2] == MOTOR_READY_FLAG_2)
            {
                motor_x_ready = 1; // X轴电机到位标志设置
                my_printf(&huart1, "X_Motor_Ready\r\n");
            }

            // 清空接收缓冲区
            for (i = 0; i < motor_x_rx_counter; i++)
            {
                motor_x_rx_buffer[i] = 0x00;
            }
        }
        else // 最后字节不是0x6B，数据帧错误
        {
            stop_flag_car = 2; // 设置通信错误标志
            my_printf(&huart1, "X_Comm_Error\r\n");
            // 清空接收缓冲区
            for (i = 0; i < motor_x_rx_counter; i++)
            {
                motor_x_rx_buffer[i] = 0x00;
            }
        }

        // 重置状态机和计数器
        motor_x_rx_state = 0;
        motor_x_rx_counter = 0;
    }
}
/**
 * @brief Y轴电机编码器数据处理函数
 * @param com_data 接收到的单字节数据
 */
void Motor_Y_Receive_Data(uint8_t com_data)
{
    uint8_t i;

    // 状态0：等待起始字节(0x01)
    if (motor_y_rx_state == 0 && com_data == MOTOR_START_BYTE_01)
    {
        motor_y_rx_state = 1;                               // 进入数据接收状态
        motor_y_rx_buffer[motor_y_rx_counter++] = com_data; // 存储起始字节
    }
    // 状态1：接收数据字节
    else if (motor_y_rx_state == 1)
    {
        motor_y_rx_buffer[motor_y_rx_counter++] = com_data; // 存储数据字节

        // 如果缓冲区满或接收到结束字节0x6B，进入解析状态
        if (motor_y_rx_counter >= MOTOR_RX_BUFFER_SIZE || com_data == MOTOR_END_BYTE)
        {
            motor_y_rx_state = 2;
        }
    }

    // 状态2：解析接收到的数据
    if (motor_y_rx_state == 2)
    {
        // 检查最后一个字节是否为0x6B，验证数据完整性
        if (motor_y_rx_buffer[motor_y_rx_counter - 1] == MOTOR_END_BYTE)
        {
            // 检查0x01起始字节和到位标识字节0xFD 0x9F
            if (motor_y_rx_buffer[0] == MOTOR_START_BYTE_01 &&
                motor_y_rx_buffer[1] == MOTOR_READY_FLAG_1 &&
                motor_y_rx_buffer[2] == MOTOR_READY_FLAG_2)
            {
                motor_y_ready = 1; // Y轴电机到位标志设置
                my_printf(&huart1, "Y_Motor_Ready\r\n");
            }

            // 清空接收缓冲区
            for (i = 0; i < motor_y_rx_counter; i++)
            {
                motor_y_rx_buffer[i] = 0x00;
            }
        }
        else // 最后字节不是0x6B，数据帧错误
        {
            stop_flag_car = 2; // 设置通信错误标志
            my_printf(&huart1, "Y_Comm_Error\r\n");
            // 清空接收缓冲区
            for (i = 0; i < motor_y_rx_counter; i++)
            {
                motor_y_rx_buffer[i] = 0x00;
            }
        }

        // 重置状态机和计数器
        motor_y_rx_state = 0;
        motor_y_rx_counter = 0;
    }
}
/* ========== 编码器状态检测API函数 ========== */

/**
 * @brief 检查电机是否准备好进行下一步操作
 * @return 0: 电机运动中, 1: 电机已到位, 2: 通信错误
 */
uint8_t StepMotor_Check_Ready(void)
{
    // 检查通信错误
    if (stop_flag_car == 2)
    {
        my_printf(&huart1, "电机通信错误!\r\n");
        return 2; // 通信错误
    }

    // 检查两个电机是否都到位
    if (motor_x_ready && motor_y_ready)
    {
        my_printf(&huart1, "X-Axis and Y-Axis motors are both in position, ready for next operation!\r\n");
        return 1; // 电机已到位
    }

    return 0; // 电机运动中
}

/**
 * @brief 清除电机到位标志，为下一次运动做准备
 */
void StepMotor_Clear_Ready_Flags(void)
{
    motor_x_ready = 0;
    motor_y_ready = 0;
    stop_flag_car = 0;
    my_printf(&huart1, "Motor ready flags cleared, ready for next movement!\r\n");
}

/**
 * @brief 获取指定电机的到位状态
 * @param motor_id 电机ID (1: X轴电机, 2: Y轴电机)
 * @return 1: 电机到位, 0: 电机运动中
 */
uint8_t StepMotor_Get_Motor_Status(uint8_t motor_id)
{
    if (motor_id == 1)
        return motor_x_ready;
    else if (motor_id == 2)
        return motor_y_ready;
    else
        return 0;
}

/**
 * @brief 独立的编码器初始化函数
 */
void StepMotor_Init_Encoder(void)
{
    StepMotor_Clear_Ready_Flags();                          // 清除编码器到位标志
    HAL_UART_Receive_IT(&MOTOR_X_UART, &motor_x_rx_byte, 1); // 启动X轴编码器中断接收
    HAL_UART_Receive_IT(&MOTOR_Y_UART, &motor_y_rx_byte, 1); // 启动Y轴编码器中断接收
    // Load_Origin_Flags_From_Flash();                         // 暂时注释掉，避免编译错误
    my_printf(&huart1, "Encoder initialization completed!\r\n");
}

/* ========== 回零功能实现 ========== */

/**
 * @brief 设置当前位置为原点
 * @param motor_id 电机ID (1: X轴, 2: Y轴, 0: 所有轴)
 * @param save_to_flash 是否保存到Flash (true: 保存, false: 不保存)
 */
void StepMotor_Set_Current_As_Origin(uint8_t motor_id, bool save_to_flash)
{
    switch(motor_id)
    {
        case 1: // X轴
            Emm_V5_Origin_Set_O(&MOTOR_X_UART, MOTOR_X_ADDR, save_to_flash);
            motor_x_pulse_count = 0;  // 重置脉冲计数
            my_printf(&huart1, "X-Axis: Set current position as origin%s\r\n",
                     save_to_flash ? " (Saved to Flash)" : "");
            break;

        case 2: // Y轴
            Emm_V5_Origin_Set_O(&MOTOR_Y_UART, MOTOR_Y_ADDR, save_to_flash);
            motor_y_pulse_count = 0;  // 重置脉冲计数
            my_printf(&huart1, "Y-Axis: Set current position as origin%s\r\n",
                     save_to_flash ? " (Saved to Flash)" : "");
            break;

        case 0: // 所有轴
            Emm_V5_Origin_Set_O(&MOTOR_X_UART, MOTOR_X_ADDR, save_to_flash);
            Emm_V5_Origin_Set_O(&MOTOR_Y_UART, MOTOR_Y_ADDR, save_to_flash);
            motor_x_pulse_count = 0;  // 重置X轴脉冲计数
            motor_y_pulse_count = 0;  // 重置Y轴脉冲计数
            my_printf(&huart1, "All Axes: Set current position as origin%s\r\n",
                     save_to_flash ? " (Saved to Flash)" : "");
            break;

        default:
            my_printf(&huart1, "Error: Invalid motor_id %d\r\n", motor_id);
            break;
    }
}

/**
 * @brief 将当前位置重置为零点
 * @param motor_id 电机ID (1: X轴, 2: Y轴, 0: 所有轴)
 */
void StepMotor_Reset_Position_To_Zero(uint8_t motor_id)
{
    switch(motor_id)
    {
        case 1: // X轴
            Emm_V5_Reset_CurPos_To_Zero(&MOTOR_X_UART, MOTOR_X_ADDR);
            motor_x_pulse_count = 0;  // 重置脉冲计数
            my_printf(&huart1, "X-Axis: Position reset to zero\r\n");
            break;

        case 2: // Y轴
            Emm_V5_Reset_CurPos_To_Zero(&MOTOR_Y_UART, MOTOR_Y_ADDR);
            motor_y_pulse_count = 0;  // 重置脉冲计数
            my_printf(&huart1, "Y-Axis: Position reset to zero\r\n");
            break;

        case 0: // 所有轴
            Emm_V5_Reset_CurPos_To_Zero(&MOTOR_X_UART, MOTOR_X_ADDR);
            Emm_V5_Reset_CurPos_To_Zero(&MOTOR_Y_UART, MOTOR_Y_ADDR);
            motor_x_pulse_count = 0;  // 重置X轴脉冲计数
            motor_y_pulse_count = 0;  // 重置Y轴脉冲计数
            my_printf(&huart1, "All Axes: Position reset to zero\r\n");
            break;

        default:
            my_printf(&huart1, "Error: Invalid motor_id %d\r\n", motor_id);
            break;
    }
}

/**
 * @brief 单轴回零运动
 * @param motor_id 电机ID (1: X轴, 2: Y轴)
 * @param home_mode 回零模式 (0: 限位开关, 1: 编码器Z相, 2: 堵转检测, 3: 手动停止)
 * @param wait_complete 是否等待完成 (true: 等待, false: 立即返回)
 */
void StepMotor_Home_Single_Motor(uint8_t motor_id, uint8_t home_mode, bool wait_complete)
{
    const char* mode_names[] = {"Limit Switch", "Encoder Z", "Stall Detect", "Manual Stop"};

    if(home_mode > 3) {
        my_printf(&huart1, "Error: Invalid home_mode %d\r\n", home_mode);
        return;
    }

    switch(motor_id)
    {
        case 1: // X轴
            my_printf(&huart1, "X-Axis: Starting homing with %s mode\r\n", mode_names[home_mode]);
            Emm_V5_Origin_Trigger_Return(&MOTOR_X_UART, MOTOR_X_ADDR, home_mode, MOTOR_SYNC_FLAG);

            if(wait_complete) {
                my_printf(&huart1, "X-Axis: Waiting for homing complete...\r\n");
                // 这里可以添加等待逻辑，检查编码器反馈或超时
                HAL_Delay(100); // 简单延时，实际应用中应该检查状态
            }
            break;

        case 2: // Y轴
            my_printf(&huart1, "Y-Axis: Starting homing with %s mode\r\n", mode_names[home_mode]);
            Emm_V5_Origin_Trigger_Return(&MOTOR_Y_UART, MOTOR_Y_ADDR, home_mode, MOTOR_SYNC_FLAG);

            if(wait_complete) {
                my_printf(&huart1, "Y-Axis: Waiting for homing complete...\r\n");
                // 这里可以添加等待逻辑，检查编码器反馈或超时
                HAL_Delay(100); // 简单延时，实际应用中应该检查状态
            }
            break;

        default:
            my_printf(&huart1, "Error: Invalid motor_id %d for single motor homing\r\n", motor_id);
            break;
    }
}

/**
 * @brief 双轴同时回零
 * @param home_mode 回零模式 (0: 限位开关, 1: 编码器Z相, 2: 堵转检测, 3: 手动停止)
 * @param wait_complete 是否等待完成 (true: 等待, false: 立即返回)
 */
void StepMotor_Home_All_Motors(uint8_t home_mode, bool wait_complete)
{
    const char* mode_names[] = {"Limit Switch", "Encoder Z", "Stall Detect", "Manual Stop"};

    if(home_mode > 3) {
        my_printf(&huart1, "Error: Invalid home_mode %d\r\n", home_mode);
        return;
    }

    my_printf(&huart1, "All Motors: Starting homing with %s mode\r\n", mode_names[home_mode]);

    // 同时启动X轴和Y轴回零
    Emm_V5_Origin_Trigger_Return(&MOTOR_X_UART, MOTOR_X_ADDR, home_mode, MOTOR_SYNC_FLAG);
    Emm_V5_Origin_Trigger_Return(&MOTOR_Y_UART, MOTOR_Y_ADDR, home_mode, MOTOR_SYNC_FLAG);

    if(wait_complete) {
        my_printf(&huart1, "All Motors: Waiting for homing complete...\r\n");
        // 这里可以添加等待逻辑，检查两个轴的编码器反馈
        HAL_Delay(200); // 简单延时，实际应用中应该检查状态
        my_printf(&huart1, "All Motors: Homing sequence completed\r\n");
    }
}

/**
 * @brief 带超时的回零运动
 * @param motor_id 电机ID (1: X轴, 2: Y轴)
 * @param home_mode 回零模式 (0: 限位开关, 1: 编码器Z相, 2: 堵转检测, 3: 手动停止)
 * @param timeout_ms 超时时间(毫秒)
 * @return 回零状态 (HOME_STATUS_SUCCESS, HOME_STATUS_TIMEOUT, HOME_STATUS_ERROR)
 */
uint8_t StepMotor_Home_With_Timeout(uint8_t motor_id, uint8_t home_mode, uint32_t timeout_ms)
{
    const char* mode_names[] = {"Limit Switch", "Encoder Z", "Stall Detect", "Manual Stop"};
    uint32_t start_time = HAL_GetTick();

    if(home_mode > 3) {
        my_printf(&huart1, "Error: Invalid home_mode %d\r\n", home_mode);
        return HOME_STATUS_ERROR;
    }

    if(motor_id < 1 || motor_id > 2) {
        my_printf(&huart1, "Error: Invalid motor_id %d\r\n", motor_id);
        return HOME_STATUS_ERROR;
    }

    // 启动回零运动
    switch(motor_id)
    {
        case 1: // X轴
            my_printf(&huart1, "X-Axis: Starting homing with %s mode (Timeout: %dms)\r\n",
                     mode_names[home_mode], (int)timeout_ms);
            Emm_V5_Origin_Trigger_Return(&MOTOR_X_UART, MOTOR_X_ADDR, home_mode, MOTOR_SYNC_FLAG);
            break;

        case 2: // Y轴
            my_printf(&huart1, "Y-Axis: Starting homing with %s mode (Timeout: %dms)\r\n",
                     mode_names[home_mode], (int)timeout_ms);
            Emm_V5_Origin_Trigger_Return(&MOTOR_Y_UART, MOTOR_Y_ADDR, home_mode, MOTOR_SYNC_FLAG);
            break;
    }

    // 等待回零完成或超时
    while((HAL_GetTick() - start_time) < timeout_ms)
    {
        // 检查电机状态 (这里可以添加编码器反馈检查)
        uint8_t motor_status = StepMotor_Get_Motor_Status(motor_id);
        if(motor_status == 1) { // 电机到位
            my_printf(&huart1, "Motor %d: Homing completed successfully\r\n", motor_id);
            return HOME_STATUS_SUCCESS;
        }

        HAL_Delay(10); // 短暂延时
    }

    // 超时处理
    my_printf(&huart1, "Motor %d: Homing timeout after %dms\r\n", motor_id, (int)timeout_ms);
    return HOME_STATUS_TIMEOUT;
}

/**
 * @brief 中断回零运动
 * @param motor_id 电机ID (1: X轴, 2: Y轴, 0: 所有轴)
 */
void StepMotor_Interrupt_Homing(uint8_t motor_id)
{
    switch(motor_id)
    {
        case 1: // X轴
            Emm_V5_Origin_Interrupt(&MOTOR_X_UART, MOTOR_X_ADDR);
            my_printf(&huart1, "X-Axis: Homing interrupted\r\n");
            break;

        case 2: // Y轴
            Emm_V5_Origin_Interrupt(&MOTOR_Y_UART, MOTOR_Y_ADDR);
            my_printf(&huart1, "Y-Axis: Homing interrupted\r\n");
            break;

        case 0: // 所有轴
            Emm_V5_Origin_Interrupt(&MOTOR_X_UART, MOTOR_X_ADDR);
            Emm_V5_Origin_Interrupt(&MOTOR_Y_UART, MOTOR_Y_ADDR);
            my_printf(&huart1, "All Motors: Homing interrupted\r\n");
            break;

        default:
            my_printf(&huart1, "Error: Invalid motor_id %d\r\n", motor_id);
            break;
    }
}

/* ========== 智能零点管理功能实现 ========== */

// 全局变量：记录原点是否已设置（需要从Flash读取）
static uint8_t origin_set_flags = 0;  // bit0: X轴原点已设置, bit1: Y轴原点已设置

/**
 * @brief 从Flash读取原点设置状态（简化版本，实际应用中可以使用EEPROM或Flash存储）
 * @note 这里使用简化的方法，实际项目中建议使用专门的Flash存储区域
 */

/**
 * @brief 一次性设置原点（自动保存到Flash）
 * @param motor_id 电机ID (1: X轴, 2: Y轴, 0: 所有轴)
 * @param home_mode 回零模式 (0: 限位开关, 1: 编码器Z相, 2: 堵转检测, 3: 手动停止)
 * @note 此函数会检查原点是否已设置，如果已设置则跳过
 */
void StepMotor_Setup_Origin_Once(uint8_t motor_id, uint8_t home_mode)
{
    const char* mode_names[] = {"Limit Switch", "Encoder Z", "Stall Detect", "Manual Stop"};

    if(home_mode > 3) {
        my_printf(&huart1, "Error: Invalid home_mode %d\r\n", home_mode);
        return;
    }

    switch(motor_id)
    {
        case 1: // X轴
            if(origin_set_flags & 0x01) {
                my_printf(&huart1, "X-Axis: Origin already set, skipping setup\r\n");
                return;
            }

            my_printf(&huart1, "X-Axis: Setting up origin for the FIRST TIME using %s mode\r\n", mode_names[home_mode]);

            // 执行回零运动
            uint8_t x_result = StepMotor_Home_With_Timeout(1, home_mode, 15000);
            if(x_result == HOME_STATUS_SUCCESS) {
                // 设置原点并保存到Flash
                StepMotor_Set_Current_As_Origin(1, true);
                origin_set_flags |= 0x01;  // 标记X轴原点已设置
                // Save_Origin_Flags_To_Flash();  // 暂时注释掉
                my_printf(&huart1, "X-Axis: Origin setup completed and saved to Flash\r\n");
            } else {
                my_printf(&huart1, "X-Axis: Origin setup failed with status %d\r\n", x_result);
            }
            break;

        case 2: // Y轴
            if(origin_set_flags & 0x02) {
                my_printf(&huart1, "Y-Axis: Origin already set, skipping setup\r\n");
                return;
            }

            my_printf(&huart1, "Y-Axis: Setting up origin for the FIRST TIME using %s mode\r\n", mode_names[home_mode]);

            // 执行回零运动
            uint8_t y_result = StepMotor_Home_With_Timeout(2, home_mode, 15000);
            if(y_result == HOME_STATUS_SUCCESS) {
                // 设置原点并保存到Flash
                StepMotor_Set_Current_As_Origin(2, true);
                origin_set_flags |= 0x02;  // 标记Y轴原点已设置
                // Save_Origin_Flags_To_Flash();  // 暂时注释掉
                my_printf(&huart1, "Y-Axis: Origin setup completed and saved to Flash\r\n");
            } else {
                my_printf(&huart1, "Y-Axis: Origin setup failed with status %d\r\n", y_result);
            }
            break;

        case 0: // 所有轴
            my_printf(&huart1, "All Axes: Setting up origins for the FIRST TIME\r\n");
            StepMotor_Setup_Origin_Once(1, home_mode);  // 递归调用设置X轴
            StepMotor_Setup_Origin_Once(2, home_mode);  // 递归调用设置Y轴
            break;

        default:
            my_printf(&huart1, "Error: Invalid motor_id %d\r\n", motor_id);
            break;
    }
}

/**
 * @brief 检查原点是否已设置
 * @param motor_id 电机ID (1: X轴, 2: Y轴, 0: 检查所有轴)
 * @return 1: 已设置, 0: 未设置, 2: 部分设置(仅当motor_id=0时)
 */
uint8_t StepMotor_Check_Origin_Status(uint8_t motor_id)
{
    switch(motor_id)
    {
        case 1: // X轴
            return (origin_set_flags & 0x01) ? 1 : 0;

        case 2: // Y轴
            return (origin_set_flags & 0x02) ? 1 : 0;

        case 0: // 所有轴
            if((origin_set_flags & 0x03) == 0x03) {
                return 1;  // 两轴都已设置
            } else if(origin_set_flags & 0x03) {
                return 2;  // 部分设置
            } else {
                return 0;  // 都未设置
            }

        default:
            my_printf(&huart1, "Error: Invalid motor_id %d\r\n", motor_id);
            return 0;
    }
}

/**
 * @brief 返回到已设置的原点
 * @param motor_id 电机ID (1: X轴, 2: Y轴, 0: 所有轴)
 * @param wait_complete 是否等待完成
 * @note 此函数会检查原点是否已设置，如果未设置会提示先设置原点
 */
void StepMotor_Return_To_Origin(uint8_t motor_id, bool wait_complete)
{
    switch(motor_id)
    {
        case 1: // X轴
            if(!(origin_set_flags & 0x01)) {
                my_printf(&huart1, "X-Axis: Origin not set! Please call StepMotor_Setup_Origin_Once() first\r\n");
                return;
            }
            my_printf(&huart1, "X-Axis: Returning to saved origin...\r\n");
            Emm_V5_Origin_Trigger_Return(&MOTOR_X_UART, MOTOR_X_ADDR, HOME_MODE_LIMIT_SWITCH, MOTOR_SYNC_FLAG);
            if(wait_complete) {
                HAL_Delay(100);
                my_printf(&huart1, "X-Axis: Returned to origin\r\n");
            }
            break;

        case 2: // Y轴
            if(!(origin_set_flags & 0x02)) {
                my_printf(&huart1, "Y-Axis: Origin not set! Please call StepMotor_Setup_Origin_Once() first\r\n");
                return;
            }
            my_printf(&huart1, "Y-Axis: Returning to saved origin...\r\n");
            Emm_V5_Origin_Trigger_Return(&MOTOR_Y_UART, MOTOR_Y_ADDR, HOME_MODE_LIMIT_SWITCH, MOTOR_SYNC_FLAG);
            if(wait_complete) {
                HAL_Delay(100);
                my_printf(&huart1, "Y-Axis: Returned to origin\r\n");
            }
            break;

        case 0: // 所有轴
            if((origin_set_flags & 0x03) != 0x03) {
                my_printf(&huart1, "Not all axes have origin set! X:%s, Y:%s\r\n",
                         (origin_set_flags & 0x01) ? "SET" : "NOT SET",
                         (origin_set_flags & 0x02) ? "SET" : "NOT SET");
                my_printf(&huart1, "Please call StepMotor_Setup_Origin_Once() first\r\n");
                return;
            }
            my_printf(&huart1, "All Axes: Returning to saved origins...\r\n");
            Emm_V5_Origin_Trigger_Return(&MOTOR_X_UART, MOTOR_X_ADDR, HOME_MODE_LIMIT_SWITCH, MOTOR_SYNC_FLAG);
            Emm_V5_Origin_Trigger_Return(&MOTOR_Y_UART, MOTOR_Y_ADDR, HOME_MODE_LIMIT_SWITCH, MOTOR_SYNC_FLAG);
            if(wait_complete) {
                HAL_Delay(200);
                my_printf(&huart1, "All Axes: Returned to origins\r\n");
            }
            break;

        default:
            my_printf(&huart1, "Error: Invalid motor_id %d\r\n", motor_id);
            break;
    }
}

/**
 * @brief 智能回零序列（首次设置+后续回零）
 * @note 此函数会自动判断：
 *       - 如果原点未设置，执行首次设置原点（保存到Flash）
 *       - 如果原点已设置，直接返回到原点
 */
void StepMotor_Smart_Home_Sequence(void)
{
    my_printf(&huart1, "=== Smart Homing Sequence Started ===\r\n");

    // 检查原点设置状态
    uint8_t origin_status = StepMotor_Check_Origin_Status(0);

    switch(origin_status)
    {
        case 0: // 都未设置
            my_printf(&huart1, "No origins set. Performing FIRST-TIME origin setup...\r\n");
            StepMotor_Setup_Origin_Once(0, HOME_MODE_LIMIT_SWITCH);  // 使用限位开关模式
            break;

        case 1: // 都已设置
            my_printf(&huart1, "Origins already set. Returning to saved origins...\r\n");
            StepMotor_Return_To_Origin(0, true);
            break;

        case 2: // 部分设置
            my_printf(&huart1, "Partial origin setup detected. Completing setup...\r\n");
            if(!(origin_set_flags & 0x01)) {
                my_printf(&huart1, "Setting up X-axis origin...\r\n");
                StepMotor_Setup_Origin_Once(1, HOME_MODE_LIMIT_SWITCH);
            }
            if(!(origin_set_flags & 0x02)) {
                my_printf(&huart1, "Setting up Y-axis origin...\r\n");
                StepMotor_Setup_Origin_Once(2, HOME_MODE_LIMIT_SWITCH);
            }
            break;
    }

    // 最终状态报告
    my_printf(&huart1, "=== Smart Homing Sequence Complete ===\r\n");
    my_printf(&huart1, "Origin Status - X:%s, Y:%s\r\n",
             (origin_set_flags & 0x01) ? "SET" : "NOT SET",
             (origin_set_flags & 0x02) ? "SET" : "NOT SET");
}
/**
 * @brief 控制XY轴电机转动固定脉冲数并等待到位（带超时保护）
 * @param x_pulses X轴脉冲数，范围可正可负
 * @param y_pulses Y轴脉冲数，范围可正可负
 * @param timeout_ms 超时时间（毫秒）
 * @return 0: 超时, 1: 电机已到位, 2: 通信错误
 */
uint8_t StepMotor_Move_Pulses_Wait(int32_t x_pulses, int32_t y_pulses, uint32_t timeout_ms)
{
    // 清除之前的到位标志
    StepMotor_Clear_Ready_Flags();

    // 开始电机运动
    StepMotor_Move_Pulses(x_pulses, y_pulses);
    my_printf(&huart1, "Starting motor movement: X=%ld, Y=%ld pulses\r\n", x_pulses, y_pulses);

    // 等待电机到位或超时
    uint32_t start_time = HAL_GetTick();
    while (HAL_GetTick() - start_time < timeout_ms)
    {
        uint8_t status = StepMotor_Check_Ready();
        if (status == 1)
        {
            my_printf(&huart1, "电机运动完成，已到位!\r\n");
            return 1; // 电机已到位
        }
        else if (status == 2)
        {
            my_printf(&huart1, "电机通信错误!\r\n");
            return 2; // 通信错误
        }
        HAL_Delay(10); // 短暂延时，避免过度占用CPU
    }

    my_printf(&huart1, "电机运动超时 (%lu ms)!\r\n", timeout_ms);
    return 0; // 超时
}

/**
 * @brief 控制XY轴电机转动固定脉冲数（非阻塞版本，立即返回）
 * @param x_pulses X轴脉冲数，范围可正可负
 * @param y_pulses Y轴脉冲数，范围可正可负
 */
void StepMotor_Move_Pulses_Async(int32_t x_pulses, int32_t y_pulses)
{
    // 清除之前的到位标志
    StepMotor_Clear_Ready_Flags();

    // 开始电机运动
    StepMotor_Move_Pulses(x_pulses, y_pulses);
    my_printf(&huart1, "Async Move: X=%ld Y=%ld\r\n", x_pulses, y_pulses);
}

/* ========== 脉冲计数管理API函数 ========== */

/**
 * @brief 获取指定电机的当前脉冲计数
 * @param motor_id 电机ID (1: X轴电机, 2: Y轴电机)
 * @return 当前脉冲计数值
 */
int32_t StepMotor_Get_Pulse_Count(uint8_t motor_id)
{
    if (motor_id == 1) {
        return motor_x_pulse_count;
    } else if (motor_id == 2) {
        return motor_y_pulse_count;
    }
    return 0;
}

/**
 * @brief 获取指定电机的目标脉冲数
 * @param motor_id 电机ID (1: X轴电机, 2: Y轴电机)
 * @return 目标脉冲数
 */
int32_t StepMotor_Get_Target_Pulses(uint8_t motor_id)
{
    if (motor_id == 1) {
        return motor_x_target_pulses;
    } else if (motor_id == 2) {
        return motor_y_target_pulses;
    }
    return 0;
}

/**
 * @brief 重置指定电机的脉冲计数
 * @param motor_id 电机ID (1: X轴电机, 2: Y轴电机, 0: 重置所有)
 */
void StepMotor_Reset_Pulse_Count(uint8_t motor_id)
{
    if (motor_id == 1 || motor_id == 0) {
        motor_x_pulse_count = 0;
        motor_x_target_pulses = 0;
    }
    if (motor_id == 2 || motor_id == 0) {
        motor_y_pulse_count = 0;
        motor_y_target_pulses = 0;
    }
    my_printf(&huart1, "Pulse Reset: Motor_%d\r\n", motor_id);
}

/**
 * @brief 获取所有电机的脉冲信息
 * @param x_current 返回X轴当前脉冲计数
 * @param y_current 返回Y轴当前脉冲计数
 * @param x_target 返回X轴目标脉冲数
 * @param y_target 返回Y轴目标脉冲数
 */
void StepMotor_Get_All_Pulse_Info(int32_t *x_current, int32_t *y_current,
                                  int32_t *x_target, int32_t *y_target)
{
    if (x_current) *x_current = motor_x_pulse_count;
    if (y_current) *y_current = motor_y_pulse_count;
    if (x_target) *x_target = motor_x_target_pulses;
    if (y_target) *y_target = motor_y_target_pulses;
}

/**
 * @brief 打印当前脉冲状态信息
 */
void StepMotor_Print_Pulse_Status(void)
{
    my_printf(&huart1, "Pulse Status:\r\n");
    my_printf(&huart1, "X: Current=%ld Target=%ld\r\n", motor_x_pulse_count, motor_x_target_pulses);
    my_printf(&huart1, "Y: Current=%ld Target=%ld\r\n", motor_y_pulse_count, motor_y_target_pulses);
}

void StepMotor_Run_Continuous(int16_t x_speed_rpm, int16_t y_speed_rpm)
{
    uint8_t x_dir, y_dir;
    uint16_t x_speed_abs, y_speed_abs;

    if (x_speed_rpm >= 0) {
        x_dir = 0;
        x_speed_abs = (uint16_t)x_speed_rpm;
    } else {
        x_dir = 1;
        x_speed_abs = (uint16_t)(-x_speed_rpm);
    }

    if (y_speed_rpm >= 0) {
        y_dir = 0;
        y_speed_abs = (uint16_t)y_speed_rpm;
    } else {
        y_dir = 1;
        y_speed_abs = (uint16_t)(-y_speed_rpm);
    }

    if (x_speed_abs > MOTOR_MAX_SPEED) x_speed_abs = MOTOR_MAX_SPEED;
    if (y_speed_abs > MOTOR_MAX_SPEED) y_speed_abs = MOTOR_MAX_SPEED;

    my_printf(&huart1, "Cmd: X(dir=%d, spd=%d), Y(dir=%d, spd=%d)\r\n", 
              x_dir, x_speed_abs, y_dir, y_speed_abs);

    Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, x_speed_abs, MOTOR_ACCEL, MOTOR_SYNC_FLAG);
    Emm_V5_Vel_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, y_speed_abs, MOTOR_ACCEL, MOTOR_SYNC_FLAG);

    my_printf(&huart1, "Motor Running: X=%d RPM, Y=%d RPM\r\n", x_speed_rpm, y_speed_rpm);
}

void StepMotor_Set_High_Speed(int8_t x_percent, int8_t y_percent)
{
    uint8_t x_dir, y_dir;
    uint16_t x_speed, y_speed;
    
    // 限制百分比范围
//    if (x_percent > 100) x_percent = 100;
//    if (x_percent < -100) x_percent = -100;
//    if (y_percent > 100) y_percent = 100;
//    if (y_percent < -100) y_percent = -100;
    
    // 处理方向
    if (x_percent >= 0) {
        x_dir = 0; // CW方向
    } else {
        x_dir = 1;              // CCW方向
        x_percent = -x_percent; // 取绝对值
    }
    
    if (y_percent >= 0) {
        y_dir = 0; // CW方向
    } else {
        y_dir = 1;              // CCW方向
        y_percent = -y_percent; // 取绝对值
    }
    
    // 计算实际速度值(百分比转换为更高的RPM)
    x_speed = (uint16_t)((x_percent * 50) / 100);
    y_speed = (uint16_t)((y_percent * 50)/ 100);
    
    // 控制X电机
    Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, x_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);
    
    // 控制Y电机
    Emm_V5_Vel_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, y_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);
}
